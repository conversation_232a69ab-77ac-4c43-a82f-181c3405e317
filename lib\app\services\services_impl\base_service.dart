import 'package:get/get.dart';
import '../services_interface/app_services_interface.dart';

/// Abstract base class for all initializable services
/// Provides common initialization logic and state management
/// 
/// This class implements the common patterns for service initialization
/// and lifecycle management, reducing boilerplate in concrete services.
abstract class InitializableService extends GetxService
    implements IInitializableService {
  
  // ========================================
  // PRIVATE STATE
  // ========================================
  
  bool _initialized = false;
  bool _started = false;

  // ========================================
  // ABSTRACT METHODS - Must be implemented by subclasses
  // ========================================
  
  /// Abstract method for specific initialization logic
  /// Each service must implement this method to define its initialization logic
  Future<void> _init();

  // ========================================
  // CORE LIFECYCLE IMPLEMENTATION
  // ========================================
  
  @override
  Future<void> initialize() async {
    if (_initialized) return;
    
    try {
      await _init();
      _initialized = true;
    } catch (e) {
      _initialized = false;
      rethrow;
    }
  }

  @override
  Future<dynamic> ensureInitialized() async {
    if (!_initialized) {
      await initialize();
    }
    return this;
  }

  @override
  bool get isInitialized => _initialized;

  @override
  bool get isReady => _initialized;

  // ========================================
  // OPTIONAL LIFECYCLE HOOKS - Default implementations
  // ========================================
  
  @override
  Future<void> start() async {
    await ensureInitialized();
    _started = true;
  }

  @override
  Future<void> stop() async {
    _started = false;
  }

  @override
  bool get isHealthy => isReady && _started;

  // ========================================
  // GETX LIFECYCLE HOOKS
  // ========================================
  
  @override
  void onInit() {
    super.onInit();
    // Services can override this for additional initialization
  }

  @override
  void onReady() {
    super.onReady();
    // Services can override this for post-initialization setup
  }

  @override
  void onClose() {
    // Ensure cleanup when service is disposed
    stop();
    super.onClose();
  }

  // ========================================
  // UTILITY METHODS
  // ========================================
  
  /// Check if the service is both initialized and started
  bool get isActive => isInitialized && _started;
  
  /// Get a human-readable status of the service
  String get status {
    if (!isInitialized) return 'Not Initialized';
    if (!_started) return 'Initialized but not Started';
    if (!isHealthy) return 'Unhealthy';
    return 'Active';
  }
}
