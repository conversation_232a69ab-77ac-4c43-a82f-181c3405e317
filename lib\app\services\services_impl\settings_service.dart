import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../data/models/entities/sittings_model.dart';
import '../services_interface/settings_service_interface.dart'
    as settings_interface;
import '../services_interface/base_service_interface.dart';
import 'objectbox_service.dart';

/// Settings service implementation
/// Manages application settings with ObjectBox persistence
class SettingsService extends InitializableService
    implements settings_interface.ISettingsService {
  late Sittings _settings;
  ObjectboxService get _objectboxService => Get.find<ObjectboxService>();

  @override
  Future<void> _init() async {
    await loadSettings();
  }

  @override
  Future<void> loadSettings() async {
    try {
      _settings = _objectboxService.objectbox.appBox.get(1) ?? Sittings(id: 1);
    } catch (e) {
      _settings = Sittings(id: 1);
    }
  }

  @override
  Future<void> saveSettings() async {
    try {
      _objectboxService.objectbox.appBox.put(_settings);
    } catch (e) {
      throw Exception('Failed to save settings: $e');
    }
  }

  @override
  Future<void> resetToDefaults() async {
    _settings = Sittings(id: 1);
    await saveSettings();
  }

  // Language Settings
  @override
  Locale get currentLanguage {
    final languageCode = _settings.language ?? 'en';
    return Locale(
      languageCode.split('_')[0],
      languageCode.split('_').length > 1 ? languageCode.split('_')[1] : null,
    );
  }

  @override
  Future<void> setLanguage(Locale locale) async {
    _settings.language = '${locale.languageCode}_${locale.countryCode ?? ''}';
    await saveSettings();
  }

  @override
  List<Locale> get supportedLanguages => [
    const Locale('en', 'US'),
    const Locale('ar', 'SA'),
  ];

  // Theme Settings
  @override
  bool get isDarkMode => _settings.isDarkMode ?? false;

  @override
  Future<void> setDarkMode(bool enabled) async {
    _settings.isDarkMode = enabled;
    await saveSettings();
  }

  @override
  ThemeMode get themeMode => isDarkMode ? ThemeMode.dark : ThemeMode.light;

  @override
  Future<void> setThemeMode(ThemeMode mode) async {
    await setDarkMode(mode == ThemeMode.dark);
  }

  // Notification Settings
  @override
  bool get notificationsEnabled => _settings.notificationEnabled ?? true;

  @override
  Future<void> setNotificationsEnabled(bool enabled) async {
    _settings.notificationEnabled = enabled;
    await saveSettings();
  }

  @override
  bool get pushNotificationsEnabled => notificationsEnabled;

  @override
  Future<void> setPushNotificationsEnabled(bool enabled) async {
    await setNotificationsEnabled(enabled);
  }

  // Background Work Settings
  @override
  bool get backgroundWorkEnabled => _settings.backgroundWorkEnabled ?? true;

  @override
  Future<void> setBackgroundWorkEnabled(bool enabled) async {
    _settings.backgroundWorkEnabled = enabled;
    await saveSettings();
  }

  // Privacy Settings (using defaults since not in Sittings model)
  @override
  bool get analyticsEnabled => true;

  @override
  Future<void> setAnalyticsEnabled(bool enabled) async {
    // Could extend Sittings model to include this
  }

  @override
  bool get crashReportingEnabled => true;

  @override
  Future<void> setCrashReportingEnabled(bool enabled) async {
    // Could extend Sittings model to include this
  }

  // App Behavior Settings (using defaults)
  @override
  bool get autoSaveEnabled => true;

  @override
  Future<void> setAutoSaveEnabled(bool enabled) async {
    // Could extend Sittings model to include this
  }

  @override
  Duration get autoSaveInterval => const Duration(minutes: 5);

  @override
  Future<void> setAutoSaveInterval(Duration interval) async {
    // Could extend Sittings model to include this
  }

  // Generic setting operations
  @override
  T? getSetting<T>(String key, {T? defaultValue}) {
    // Basic implementation - could be extended with a Map<String, dynamic> in Sittings
    switch (key) {
      case 'isDarkMode':
        return isDarkMode as T?;
      case 'notificationsEnabled':
        return notificationsEnabled as T?;
      case 'backgroundWorkEnabled':
        return backgroundWorkEnabled as T?;
      case 'language':
        return _settings.language as T?;
      default:
        return defaultValue;
    }
  }

  @override
  Future<void> setSetting<T>(String key, T value) async {
    switch (key) {
      case 'isDarkMode':
        if (value is bool) await setDarkMode(value);
        break;
      case 'notificationsEnabled':
        if (value is bool) await setNotificationsEnabled(value);
        break;
      case 'backgroundWorkEnabled':
        if (value is bool) await setBackgroundWorkEnabled(value);
        break;
      case 'language':
        if (value is String) {
          _settings.language = value;
          await saveSettings();
        }
        break;
    }
  }

  @override
  Future<void> removeSetting(String key) async {
    switch (key) {
      case 'isDarkMode':
        _settings.isDarkMode = null;
        break;
      case 'notificationsEnabled':
        _settings.notificationEnabled = null;
        break;
      case 'backgroundWorkEnabled':
        _settings.backgroundWorkEnabled = null;
        break;
      case 'language':
        _settings.language = null;
        break;
    }
    await saveSettings();
  }

  @override
  bool hasSetting(String key) {
    switch (key) {
      case 'isDarkMode':
        return _settings.isDarkMode != null;
      case 'notificationsEnabled':
        return _settings.notificationEnabled != null;
      case 'backgroundWorkEnabled':
        return _settings.backgroundWorkEnabled != null;
      case 'language':
        return _settings.language != null;
      default:
        return false;
    }
  }

  // Bulk operations
  @override
  Map<String, dynamic> getAllSettings() {
    return {
      'isDarkMode': _settings.isDarkMode,
      'notificationsEnabled': _settings.notificationEnabled,
      'backgroundWorkEnabled': _settings.backgroundWorkEnabled,
      'language': _settings.language,
      'deviceName': _settings.deviceName,
      'isLoggedIn': _settings.isLoggedIn,
      'passCodeLock': _settings.passCodeLock,
      'localizations': _settings.localizations,
    };
  }

  @override
  Future<void> setMultipleSettings(Map<String, dynamic> settings) async {
    for (final entry in settings.entries) {
      await setSetting(entry.key, entry.value);
    }
  }

  @override
  Future<void> clearAllSettings() async {
    _settings = Sittings(id: 1);
    await saveSettings();
  }

  // Settings validation
  @override
  bool validateSettings() {
    // Basic validation - can be extended
    return true;
  }

  @override
  List<String> getInvalidSettings() {
    final invalid = <String>[];
    // Add validation logic here
    // For example, check if language is supported
    if (_settings.language != null &&
        !supportedLanguages.any(
          (locale) =>
              '${locale.languageCode}_${locale.countryCode ?? ''}' ==
              _settings.language,
        )) {
      invalid.add('language');
    }
    return invalid;
  }

  // Settings export/import
  @override
  Future<String> exportSettings() async {
    final settingsMap = getAllSettings();
    return jsonEncode(settingsMap);
  }

  @override
  Future<bool> importSettings(String settingsJson) async {
    try {
      final Map<String, dynamic> settingsMap = jsonDecode(settingsJson);
      await setMultipleSettings(settingsMap);
      return true;
    } catch (e) {
      return false;
    }
  }
}
