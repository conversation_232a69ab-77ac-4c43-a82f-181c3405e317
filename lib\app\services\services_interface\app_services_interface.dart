import 'package:get/get.dart';

/// Base interface for all application services
/// Defines the core contract that all services must implement
abstract class IInitializableService extends GetxService {
  // ========================================
  // CORE LIFECYCLE METHODS
  // ========================================

  /// Initialize the service asynchronously
  /// This method should be called once during service setup
  Future<void> initialize();

  /// Ensure the service is initialized (idempotent operation)
  /// Safe to call multiple times - will only initialize once
  /// Returns the service instance for method chaining
  Future<dynamic> ensureInitialized();

  // ========================================
  // SERVICE STATE QUERIES
  // ========================================

  /// Check if the service has been initialized
  bool get isInitialized;

  /// Check if the service is ready for use
  /// May differ from isInitialized for services with complex startup
  bool get isReady;

  // ========================================
  // OPTIONAL LIFECYCLE HOOKS
  // ========================================

  /// Called when the service should start its operations
  /// Override in services that need explicit start/stop lifecycle
  Future<void> start();

  /// Called when the service should stop its operations
  /// Override in services that need cleanup
  Future<void> stop();

  /// Called to check if the service is healthy and functioning properly
  /// Override in services that need health monitoring
  bool get isHealthy;
}
