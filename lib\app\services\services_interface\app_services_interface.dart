import 'package:get/get.dart';

/// Base interface for all initializable services
abstract class IInitializableService extends GetxService {
  /// Initialize the service asynchronously
  Future<void> initialize();

  /// Ensure the service is initialized (idempotent)
  Future<dynamic> ensureInitialized();

  /// Check if the service is initialized
  bool get isInitialized;

  /// Check if the service is ready for use
  bool get isReady;
}

