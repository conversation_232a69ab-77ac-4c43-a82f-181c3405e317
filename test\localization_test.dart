import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:deewan/app/services/services_impl/localization_service.dart';
import 'package:deewan/core/localization/translation.dart';
import 'package:deewan/app/controllers/sittings/settingscontroller.dart';

void main() {
  group('Localization System Tests', () {
    late LocalizationService localizationService;
    late SettingsController settingsController;

    setUpAll(() {
      // Initialize GetX
      Get.testMode = true;
    });

    setUp(() {
      // Reset GetX
      Get.reset();

      // Initialize services
      localizationService = LocalizationService();
      Get.put(localizationService, permanent: true);

      // Mock ObjectBox service for testing
      Get.put(_MockObjectboxService(), permanent: true);

      settingsController = SettingsController();
      Get.put(settingsController);
    });

    tearDown(() {
      Get.reset();
    });

    group('LocalizationService Tests', () {
      test('should initialize with default locale', () {
        expect(
          localizationService.currentLocale,
          equals(localizationService.defaultLocale),
        );
        expect(localizationService.textDirection, equals(TextDirection.ltr));
        expect(localizationService.isEnglish, isTrue);
        expect(localizationService.isArabic, isFalse);
      });

      test('should change locale correctly', () async {
        const arabicLocale = Locale('ar', 'SA');

        await localizationService.changeLocale(arabicLocale);

        expect(localizationService.currentLocale, equals(arabicLocale));
        expect(localizationService.textDirection, equals(TextDirection.rtl));
        expect(localizationService.isArabic, isTrue);
        expect(localizationService.isEnglish, isFalse);
        expect(localizationService.isRTL, isTrue);
      });

      test('should toggle language correctly', () async {
        // Start with English
        expect(localizationService.isEnglish, isTrue);

        // Toggle to Arabic
        await localizationService.toggleLanguage();
        expect(localizationService.isArabic, isTrue);
        expect(localizationService.isRTL, isTrue);

        // Toggle back to English
        await localizationService.toggleLanguage();
        expect(localizationService.isEnglish, isTrue);
        expect(localizationService.isRTL, isFalse);
      });

      test('should provide correct language names', () {
        expect(
          localizationService.getLanguageName(const Locale('en', 'US')),
          equals('English'),
        );
        expect(
          localizationService.getLanguageName(const Locale('ar', 'SA')),
          equals('العربية'),
        );
      });

      test('should provide correct directional padding', () {
        // Test LTR padding
        final ltrPadding = localizationService.getDirectionalPadding(
          start: 10,
          end: 20,
        );
        expect(ltrPadding.left, equals(10));
        expect(ltrPadding.right, equals(20));

        // Switch to RTL and test
        localizationService.changeLocale(const Locale('ar', 'SA'));
        final rtlPadding = localizationService.getDirectionalPadding(
          start: 10,
          end: 20,
        );
        expect(rtlPadding.left, equals(20));
        expect(rtlPadding.right, equals(10));
      });

      test('should validate supported locales', () {
        expect(
          localizationService.supportedLocales,
          contains(const Locale('en', 'US')),
        );
        expect(
          localizationService.supportedLocales,
          contains(const Locale('ar', 'SA')),
        );
        expect(localizationService.supportedLocales.length, equals(2));
      });
    });

    group('Translation Tests', () {
      test('should have all required translation keys', () {
        final translations = AppTranslation().keys;

        // Check that both locales exist
        expect(translations, containsPair('en_US', isA<Map<String, String>>()));
        expect(translations, containsPair('ar_SA', isA<Map<String, String>>()));

        final englishTranslations =
            translations['en_US'] as Map<String, String>;
        final arabicTranslations = translations['ar_SA'] as Map<String, String>;

        // Check essential keys exist in both languages
        final essentialKeys = [
          'title',
          'home',
          'settings',
          'language',
          'login',
          'logout',
          'email',
          'password',
          'save',
          'cancel',
          'confirm',
          'error',
          'success',
        ];

        for (final key in essentialKeys) {
          expect(englishTranslations, containsPair(key, isA<String>()));
          expect(arabicTranslations, containsPair(key, isA<String>()));
        }
      });

      test('should have matching keys between languages', () {
        final translations = AppTranslation().keys;
        final englishKeys = (translations['en_US'] as Map<String, String>).keys
            .toSet();
        final arabicKeys = (translations['ar_SA'] as Map<String, String>).keys
            .toSet();

        // Check that both languages have the same keys
        expect(englishKeys, equals(arabicKeys));
      });
    });

    group('SettingsController Tests', () {
      test('should initialize correctly', () {
        expect(settingsController.currentLocale, isA<Locale>());
        expect(settingsController.isDarkMode, isFalse);
        expect(settingsController.notificationsEnabled, isTrue);
      });

      test('should change language through controller', () async {
        const arabicLocale = Locale('ar', 'SA');

        await settingsController.changeLanguage(arabicLocale);

        expect(settingsController.currentLocale, equals(arabicLocale));
        expect(settingsController.isRTL, isTrue);
      });

      test('should toggle theme mode', () async {
        expect(settingsController.isDarkMode, isFalse);

        await settingsController.toggleThemeMode();

        expect(settingsController.isDarkMode, isTrue);
      });

      test('should provide available languages', () {
        final availableLanguages = settingsController.availableLanguages;

        expect(availableLanguages, isNotEmpty);
        expect(availableLanguages, contains(const Locale('en', 'US')));
        expect(availableLanguages, contains(const Locale('ar', 'SA')));
      });
    });

    group('Widget Tests', () {
      testWidgets('GetMaterialApp should build with localization', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(
          GetMaterialApp(
            translations: AppTranslation(),
            locale: const Locale('en', 'US'),
            home: Scaffold(body: Text('title'.tr)),
          ),
        );

        expect(find.text('Deewan'), findsOneWidget);
      });
    });
  });
}

// Mock ObjectBox service for testing
class _MockObjectboxService {
  final _storage = <int, Map<String, dynamic>>{};

  _MockAppBox get appBox => _MockAppBox(_storage);

  _MockObjectbox get objectbox => _MockObjectbox(this);
}

class _MockObjectbox {
  final _MockObjectboxService service;

  _MockObjectbox(this.service);

  _MockAppBox get appBox => service.appBox;
}

class _MockAppBox {
  final Map<int, Map<String, dynamic>> _storage;

  _MockAppBox(this._storage);

  void put(dynamic entity) {
    if (entity.id != null) {
      _storage[entity.id] = {
        'id': entity.id,
        'language': entity.language,
        'isDarkMode': entity.isDarkMode,
        'notificationEnabled': entity.notificationEnabled,
        'backgroundWorkEnabled': entity.backgroundWorkEnabled,
      };
    }
  }

  dynamic get(int id) {
    final data = _storage[id];
    if (data == null) return null;

    return _MockSettings(
      id: data['id'],
      language: data['language'],
      isDarkMode: data['isDarkMode'],
      notificationEnabled: data['notificationEnabled'],
      backgroundWorkEnabled: data['backgroundWorkEnabled'],
    );
  }
}

class _MockSettings {
  final int id;
  String? language;
  bool? isDarkMode;
  bool? notificationEnabled;
  bool? backgroundWorkEnabled;

  _MockSettings({
    required this.id,
    this.language,
    this.isDarkMode,
    this.notificationEnabled,
    this.backgroundWorkEnabled,
  });
}
