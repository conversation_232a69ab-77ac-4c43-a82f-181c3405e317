import 'package:get/get.dart';
import 'app_services_interface.dart';

/// Abstract base class for all initializable services
/// Provides common initialization logic and state management
abstract class InitializableService extends GetxService
    implements IInitializableService {
  bool _initialized = false;

  /// Abstract method for specific initialization
  /// Each service must implement this method to define its initialization logic
  Future<void> _init();

  @override
  Future<void> initialize() async {
    await _init();
    _initialized = true;
  }

  @override
  Future<dynamic> ensureInitialized() async {
    if (!_initialized) {
      await initialize();
    }
    return this;
  }

  @override
  bool get isInitialized => _initialized;

  @override
  bool get isReady => _initialized;
}
