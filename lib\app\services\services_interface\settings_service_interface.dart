import 'package:flutter/material.dart';
import 'app_services_interface.dart';

/// Interface for application settings management
///
/// This service manages app-wide settings and preferences, excluding
/// localization settings which are handled by ILocalizationService.
/// Focuses on user preferences, app behavior, and system configuration.
abstract class ISettingsService extends IInitializableService {
  // ========================================
  // CORE SETTINGS OPERATIONS
  // ========================================

  /// Load all settings from persistent storage
  Future<void> loadSettings();

  /// Save all settings to persistent storage
  Future<void> saveSettings();

  /// Reset all settings to default values
  Future<void> resetToDefaults();

  // ========================================
  // THEME AND APPEARANCE SETTINGS
  // ========================================

  /// Get current dark mode setting
  bool get isDarkMode;

  /// Enable or disable dark mode
  Future<void> setDarkMode(bool enabled);

  /// Get current theme mode (light, dark, system)
  ThemeMode get themeMode;

  /// Set theme mode
  Future<void> setThemeMode(ThemeMode mode);

  // ========================================
  // NOTIFICATION SETTINGS
  // ========================================

  /// Get notification enabled status
  bool get notificationsEnabled;

  /// Enable or disable notifications
  Future<void> setNotificationsEnabled(bool enabled);

  /// Get push notification enabled status
  bool get pushNotificationsEnabled;

  /// Enable or disable push notifications
  Future<void> setPushNotificationsEnabled(bool enabled);

  // ========================================
  // BACKGROUND WORK SETTINGS
  // ========================================

  /// Get background work enabled status
  bool get backgroundWorkEnabled;

  /// Enable or disable background work
  Future<void> setBackgroundWorkEnabled(bool enabled);

  // ========================================
  // PRIVACY SETTINGS
  // ========================================

  /// Get analytics enabled status
  bool get analyticsEnabled;

  /// Enable or disable analytics
  Future<void> setAnalyticsEnabled(bool enabled);

  /// Get crash reporting enabled status
  bool get crashReportingEnabled;

  /// Enable or disable crash reporting
  Future<void> setCrashReportingEnabled(bool enabled);

  // ========================================
  // APP BEHAVIOR SETTINGS
  // ========================================

  /// Get auto-save enabled status
  bool get autoSaveEnabled;

  /// Enable or disable auto-save
  Future<void> setAutoSaveEnabled(bool enabled);

  /// Get auto-save interval
  Duration get autoSaveInterval;

  /// Set auto-save interval
  Future<void> setAutoSaveInterval(Duration interval);

  // ========================================
  // GENERIC SETTING OPERATIONS
  // ========================================

  /// Get a setting value by key with optional default
  T? getSetting<T>(String key, {T? defaultValue});

  /// Set a setting value by key
  Future<void> setSetting<T>(String key, T value);

  /// Remove a setting by key
  Future<void> removeSetting(String key);

  /// Check if a setting exists
  bool hasSetting(String key);

  // ========================================
  // BULK OPERATIONS
  // ========================================

  /// Get all settings as a map
  Map<String, dynamic> getAllSettings();

  /// Set multiple settings at once
  Future<void> setMultipleSettings(Map<String, dynamic> settings);

  /// Clear all settings (reset to defaults)
  Future<void> clearAllSettings();

  // ========================================
  // SETTINGS VALIDATION
  // ========================================

  /// Validate all current settings
  bool validateSettings();

  /// Get list of invalid setting keys
  List<String> getInvalidSettings();

  // ========================================
  // SETTINGS EXPORT/IMPORT
  // ========================================

  /// Export all settings to JSON string
  Future<String> exportSettings();

  /// Import settings from JSON string
  /// Returns true if import was successful
  Future<bool> importSettings(String settingsJson);

  // ========================================
  // SETTINGS CHANGE NOTIFICATIONS
  // ========================================

  /// Stream of setting changes (key-value pairs)
  Stream<Map<String, dynamic>> get settingsChanges;

  /// Add a listener for specific setting changes
  void addSettingChangeListener(String key, Function(dynamic value) listener);

  /// Remove a setting change listener
  void removeSettingChangeListener(
    String key,
    Function(dynamic value) listener,
  );
}
