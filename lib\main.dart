import 'package:deewan/app/bindings/initial_bindings.dart';
import 'package:deewan/app/controllers/sittings/settingscontroller.dart';
import 'package:deewan/core/localization/translation.dart';
import 'package:deewan/app/services/services_impl/localization_service.dart';
import 'package:deewan/core/theme/app_theme.dart';
import 'package:deewan/app/routes/app_pages.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize all services using the bindings service initializer
  await Initialbindings.initServices();

  runApp(const Deewan());
}

// ignore: must_be_immutable

class <PERSON>wan extends StatelessWidget {
  const Deewan({super.key});

  @override
  Widget build(BuildContext context) {
    // Get services
    final localizationService = Get.find<LocalizationService>();
    final settingsController = Get.put(SettingsController());

    return Obx(
      () => GetMaterialApp(
        debugShowCheckedModeBanner: false,
        title: 'title'.tr,

        // Theme configuration with RTL support
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: settingsController.isDarkMode
            ? ThemeMode.dark
            : ThemeMode.light,

        // Localization configuration
        translations: AppTranslation(),
        locale: localizationService.currentLocale,
        fallbackLocale: localizationService.fallbackLocale,
        supportedLocales: localizationService.supportedLocales,

        // Flutter's built-in localizations
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],

        // RTL support
        builder: (context, child) {
          return Directionality(
            textDirection: localizationService.textDirection,
            child: child!,
          );
        },

        // Navigation configuration
        initialRoute: routes?[0].name,
        getPages: routes,
        defaultTransition: Transition.fade,
        binds: Initialbindings().dependencies(),
      ),
    );
  }
}
