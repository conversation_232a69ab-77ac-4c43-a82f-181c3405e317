import 'app_services_interface.dart';

/// Interface for service health monitoring
/// 
/// This interface provides health checking capabilities for services,
/// allowing monitoring of service status and performance.
abstract class IServiceHealthMonitor {
  // ========================================
  // INDIVIDUAL SERVICE HEALTH
  // ========================================
  
  /// Check if a specific service is healthy
  bool checkServiceHealth<T extends IInitializableService>();

  /// Get detailed health information for a service
  ServiceHealthInfo getServiceHealthInfo<T extends IInitializableService>();

  // ========================================
  // SYSTEM-WIDE HEALTH
  // ========================================
  
  /// Check health of all services
  Map<Type, bool> checkAllServicesHealth();

  /// Get overall system health status
  SystemHealthStatus get systemHealthStatus;

  /// Get list of unhealthy services
  List<Type> get unhealthyServices;

  // ========================================
  // HEALTH MONITORING
  // ========================================
  
  /// Start continuous health monitoring
  void startHealthMonitoring({Duration interval = const Duration(minutes: 1)});

  /// Stop health monitoring
  void stopHealthMonitoring();

  /// Get health monitoring status
  bool get isMonitoringActive;
}

/// Service health information
class ServiceHealthInfo {
  final Type serviceType;
  final bool isHealthy;
  final bool isInitialized;
  final bool isStarted;
  final DateTime lastChecked;
  final String? errorMessage;

  const ServiceHealthInfo({
    required this.serviceType,
    required this.isHealthy,
    required this.isInitialized,
    required this.isStarted,
    required this.lastChecked,
    this.errorMessage,
  });

  @override
  String toString() {
    return 'ServiceHealthInfo('
        'serviceType: $serviceType, '
        'isHealthy: $isHealthy, '
        'isInitialized: $isInitialized, '
        'isStarted: $isStarted, '
        'lastChecked: $lastChecked'
        '${errorMessage != null ? ', error: $errorMessage' : ''}'
        ')';
  }
}

/// System health status
enum SystemHealthStatus {
  /// All services are healthy and functioning properly
  healthy,
  
  /// Some services have issues but core functionality is available
  degraded,
  
  /// Critical services are failing, system may not function properly
  unhealthy,
  
  /// Health status cannot be determined
  unknown,
}

/// Extension methods for SystemHealthStatus
extension SystemHealthStatusExtension on SystemHealthStatus {
  /// Get a human-readable description of the health status
  String get description {
    switch (this) {
      case SystemHealthStatus.healthy:
        return 'All services are healthy and functioning properly';
      case SystemHealthStatus.degraded:
        return 'Some services have issues but core functionality is available';
      case SystemHealthStatus.unhealthy:
        return 'Critical services are failing, system may not function properly';
      case SystemHealthStatus.unknown:
        return 'Health status cannot be determined';
    }
  }

  /// Check if the status indicates the system is operational
  bool get isOperational {
    return this == SystemHealthStatus.healthy || 
           this == SystemHealthStatus.degraded;
  }

  /// Check if the status indicates critical issues
  bool get isCritical {
    return this == SystemHealthStatus.unhealthy;
  }
}
