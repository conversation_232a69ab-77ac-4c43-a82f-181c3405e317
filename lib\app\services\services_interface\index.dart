/// Service Interfaces Export File
///
/// This file provides a single import point for all service interfaces
/// in the application. It promotes clean architecture by separating
/// interface definitions from implementations.
///
/// The interfaces are organized into logical groups for better maintainability.

// ========================================
// CORE SERVICE INTERFACES
// ========================================

/// Base interface for all services
export 'app_services_interface.dart';

// ========================================
// SPECIFIC SERVICE INTERFACES
// ========================================

/// Database service interface
export 'objectbox_service_interface.dart';

/// Settings management interface
export 'settings_service_interface.dart';

/// Localization and internationalization interface
export 'localization_service_interface.dart';

// ========================================
// SERVICE MANAGEMENT INTERFACES
// ========================================

/// Service initialization and lifecycle management
export 'service_initializer_interface.dart';

/// Service registry for instance management
export 'service_registry_interface.dart';

/// Service health monitoring
export 'service_health_monitor_interface.dart';
