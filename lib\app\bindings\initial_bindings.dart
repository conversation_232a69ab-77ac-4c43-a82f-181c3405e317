import 'package:deewan/app/controllers/sittings/settingscontroller.dart';
import 'package:deewan/app/services/services_impl/appservices.dart';
import 'package:deewan/core/network/api_client.dart';
import 'package:deewan/core/network/network_info.dart';
import 'package:deewan/core/storage/secure_storage.dart';
import 'package:deewan/core/storage/objectbox_store.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;

class Initialbindings extends Binding {
  @override
  List<Bind> dependencies() => [
    // Core dependencies
    Bind.put(() => http.Client(), permanent: true),
    Bind.put(() => NetworkInfoImpl(), permanent: true),

    // Storage dependencies
    Bind.put(() => SecureStorageImpl(), permanent: true),
    Bind.put(() => ObjectBoxStore.instance, permanent: true),

    // Network dependencies
    Bind.put(() => ApiClient(client: Get.find<http.Client>()), permanent: true),

    // Core Services - Using lazy initialization for async services
    // ObjectBox service (initialized when first accessed)
    Bind.lazyPut<ObjectboxService>(() {
      final service = ObjectboxService();
      // Note: ensureInitialized() will be called when service is first accessed
      return service;
    }, fenix: true),

    // Settings service (depends on ObjectBox)
    Bind.lazyPut<SettingsService>(() {
      final service = SettingsService();
      // Note: ensureInitialized() will be called when service is first accessed
      return service;
    }, fenix: true),

    // Localization service
    Bind.lazyPut<LocalizationService>(() => LocalizationService(), fenix: true),

    // Note: ErrorHandler and ValidationUtils are static utility classes
    // They don't need to be registered in DI as they have no state

    // TODO: Core repositories (when implemented)
    // Bind.lazyPut<AuthRepository>(() => AuthRepositoryImpl(
    //   apiClient: Get.find<ApiClient>(),
    //   secureStorage: Get.find<SecureStorage>(),
    // ), fenix: true),

    // Controllers - Only essential app-wide controllers
    Bind.put(() => SettingsController()),

    // TODO: Add these controllers when implemented:
    // Bind.put(() => AuthController(), permanent: true),      // User authentication state
    // Bind.put(() => NetworkController(), permanent: true),   // Internet connectivity monitoring
    // Bind.put(() => NavigationController(), permanent: true), // App navigation state
    // Bind.put(() => NotificationController(), permanent: true), // Push notifications
    // Bind.put(() => AppStateController(), permanent: true),  // App lifecycle management
  ];

  /// Optional: Initialize critical services early if needed
  /// This can be called before the app starts for services that must be ready immediately
  static Future<void> initCriticalServices() async {
    // Force initialization of critical services if needed
    // await Get.find<ObjectboxService>().ensureInitialized();
    // await Get.find<SettingsService>().ensureInitialized();
  }
}
