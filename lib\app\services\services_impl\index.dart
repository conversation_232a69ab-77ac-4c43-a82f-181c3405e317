/// Service Implementations Export File
///
/// This file provides a single import point for all service implementations
/// in the application. It promotes clean architecture by providing concrete
/// implementations of the service interfaces.

// Base service class
export '../services_interface/base_service_interface.dart';

// Individual service implementations
export 'objectbox_service.dart';
export 'settings_service.dart';
export 'localization_service.dart';

// Service initialization
export 'service_initializer_impl.dart';

// Legacy export for backward compatibility
export 'appservices.dart';

// Convenience functions
export 'service_initializer_impl.dart' show initializeAppServices;
