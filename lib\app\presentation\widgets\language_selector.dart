import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/sittings/settingscontroller.dart';
import '../../services/services_impl/localization_service.dart';

/// A reusable language selector widget that can be used throughout the app
class LanguageSelector extends StatelessWidget {
  final bool showLabel;
  final bool showIcon;
  final bool compact;
  final VoidCallback? onLanguageChanged;

  const LanguageSelector({
    super.key,
    this.showLabel = true,
    this.showIcon = true,
    this.compact = false,
    this.onLanguageChanged,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SettingsController>();
    final localizationService = Get.find<LocalizationService>();

    return Obx(() {
      if (compact) {
        return _buildCompactSelector(controller, localizationService);
      } else {
        return _buildFullSelector(controller, localizationService);
      }
    });
  }

  Widget _buildCompactSelector(
    SettingsController controller,
    LocalizationService localizationService,
  ) {
    return InkWell(
      onTap: () => _showLanguageBottomSheet(controller),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (showIcon) ...[
              Icon(Icons.language, size: 20, color: Colors.grey.shade600),
              const SizedBox(width: 8),
            ],
            Text(
              _getLanguageCode(controller.currentLocale),
              style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 14),
            ),
            const SizedBox(width: 4),
            Icon(
              Icons.keyboard_arrow_down,
              size: 16,
              color: Colors.grey.shade600,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFullSelector(
    SettingsController controller,
    LocalizationService localizationService,
  ) {
    return Card(
      child: ListTile(
        leading: showIcon ? const Icon(Icons.language) : null,
        title: showLabel ? Text('language'.tr) : null,
        subtitle: Text(controller.currentLanguageName),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _getLanguageCode(controller.currentLocale),
              style: TextStyle(
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 8),
            const Icon(Icons.arrow_forward_ios, size: 16),
          ],
        ),
        onTap: () => _showLanguageBottomSheet(controller),
      ),
    );
  }

  String _getLanguageCode(Locale locale) {
    return locale.languageCode.toUpperCase();
  }

  void _showLanguageBottomSheet(SettingsController controller) {
    Get.bottomSheet(
      Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  const Icon(Icons.language, size: 24),
                  const SizedBox(width: 12),
                  Text(
                    'selectLanguage'.tr,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),

            // Language options
            ...controller.availableLanguages.map((locale) {
              final isSelected = controller.currentLocale == locale;
              final languageName = controller.getLanguageDisplayName(locale);
              final languageCode = _getLanguageCode(locale);

              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: isSelected
                      ? Colors.blue
                      : Colors.grey.shade200,
                  child: Text(
                    languageCode,
                    style: TextStyle(
                      color: isSelected ? Colors.white : Colors.grey.shade600,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
                title: Text(
                  languageName,
                  style: TextStyle(
                    fontWeight: isSelected
                        ? FontWeight.w600
                        : FontWeight.normal,
                  ),
                ),
                subtitle: Text(_getLanguageDescription(locale)),
                trailing: isSelected
                    ? const Icon(Icons.check_circle, color: Colors.blue)
                    : null,
                onTap: () {
                  controller.changeLanguage(locale);
                  Get.back();
                  onLanguageChanged?.call();
                },
              );
            }),

            const SizedBox(height: 20),
          ],
        ),
      ),
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
    );
  }

  String _getLanguageDescription(Locale locale) {
    switch (locale.languageCode) {
      case 'en':
        return 'English (United States)';
      case 'ar':
        return 'العربية (السعودية)';
      default:
        return locale.toString();
    }
  }
}

/// A simple language toggle button for quick switching
class LanguageToggleButton extends StatelessWidget {
  final double? size;
  final Color? color;
  final VoidCallback? onPressed;

  const LanguageToggleButton({
    super.key,
    this.size,
    this.color,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SettingsController>();

    return Obx(
      () => IconButton(
        icon: Icon(Icons.translate, size: size ?? 24, color: color),
        tooltip: 'changeLanguage'.tr,
        onPressed: () {
          controller.toggleLanguage();
          onPressed?.call();
        },
      ),
    );
  }
}

/// A language indicator chip
class LanguageChip extends StatelessWidget {
  final bool showLabel;
  final VoidCallback? onTap;

  const LanguageChip({super.key, this.showLabel = true, this.onTap});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SettingsController>();

    return Obx(
      () => ActionChip(
        avatar: const Icon(Icons.language, size: 16),
        label: Text(
          showLabel
              ? controller.currentLanguageName
              : controller.currentLocale.languageCode.toUpperCase(),
          style: const TextStyle(fontSize: 12),
        ),
        onPressed: onTap ?? () => _showQuickLanguageDialog(controller),
      ),
    );
  }

  void _showQuickLanguageDialog(SettingsController controller) {
    Get.dialog(
      AlertDialog(
        title: Text('changeLanguage'.tr),
        content: Text('Switch to ${controller.isRTL ? 'English' : 'العربية'}?'),
        actions: [
          TextButton(onPressed: () => Get.back(), child: Text('cancel'.tr)),
          TextButton(
            onPressed: () {
              controller.toggleLanguage();
              Get.back();
            },
            child: Text('confirm'.tr),
          ),
        ],
      ),
    );
  }
}

/// A floating action button for language switching
class LanguageFAB extends StatelessWidget {
  final VoidCallback? onPressed;
  final bool mini;

  const LanguageFAB({super.key, this.onPressed, this.mini = false});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SettingsController>();

    return Obx(
      () => FloatingActionButton(
        mini: mini,
        onPressed: () {
          controller.toggleLanguage();
          onPressed?.call();
        },
        tooltip: 'changeLanguage'.tr,
        child: Text(
          controller.currentLocale.languageCode.toUpperCase(),
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
        ),
      ),
    );
  }
}
