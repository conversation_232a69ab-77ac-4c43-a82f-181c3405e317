import 'dart:ui';
import 'package:deewan/app/services/services_impl/appservices.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../data/models/entities/sittings_model.dart';

class SettingsController extends GetxController {
  // Localization service
  LocalizationService get localizationService => LocalizationService.to;

  // Settings model
  late Sittings _settings;
  ObjectboxService get objectboxService => Get.find<ObjectboxService>();

  // Reactive variables for UI
  final RxBool _isDarkMode = false.obs;
  final RxBool _notificationsEnabled = true.obs;
  final RxBool _backgroundWorkEnabled = true.obs;

  // Getters
  bool get isDarkMode => _isDarkMode.value;
  bool get notificationsEnabled => _notificationsEnabled.value;
  bool get backgroundWorkEnabled => _backgroundWorkEnabled.value;
  Locale get currentLocale => localizationService.currentLocale;
  TextDirection get textDirection => localizationService.textDirection;
  bool get isRTL => localizationService.isRTL;
  String get currentLanguageName => localizationService.currentLanguageName;

  @override
  void onInit() {
    super.onInit();
    _initializeSettings();
  }

  /// Initialize settings from storage
  void _initializeSettings() {
    try {
      _settings = objectboxService.objectbox.appBox.get(1) ?? Sittings(id: 1);

      // Initialize reactive variables from stored settings
      _isDarkMode.value = _settings.isDarkMode ?? false;
      _notificationsEnabled.value = _settings.notificationEnabled ?? true;
      _backgroundWorkEnabled.value = _settings.backgroundWorkEnabled ?? true;

      print('Settings initialized successfully');
    } catch (e) {
      print('Error initializing settings: $e');
      _settings = Sittings(id: 1);
    }
  }

  /// Change language using the localization service
  Future<void> changeLanguage(Locale locale) async {
    try {
      await localizationService.changeLocale(locale);
      print(
        'Language changed to: ${locale.languageCode}_${locale.countryCode}',
      );
    } catch (e) {
      print('Error changing language: $e');
      Get.snackbar(
        'error'.tr,
        'Failed to change language',
        snackPosition: SnackPosition.bottom,
      );
    }
  }

  /// Toggle between supported languages
  Future<void> toggleLanguage() async {
    await localizationService.toggleLanguage();
  }

  /// Change theme mode
  Future<void> changeThemeMode(bool isDark) async {
    try {
      _isDarkMode.value = isDark;
      _settings.isDarkMode = isDark;
      await _saveSettings();

      // Update app theme
      Get.changeThemeMode(isDark ? ThemeMode.dark : ThemeMode.light);

      print('Theme mode changed to: ${isDark ? 'dark' : 'light'}');
    } catch (e) {
      print('Error changing theme mode: $e');
      Get.snackbar(
        'error'.tr,
        'Failed to change theme',
        snackPosition: SnackPosition.bottom,
      );
    }
  }

  /// Toggle theme mode
  Future<void> toggleThemeMode() async {
    await changeThemeMode(!_isDarkMode.value);
  }

  /// Change notification settings
  Future<void> changeNotificationSettings(bool enabled) async {
    try {
      _notificationsEnabled.value = enabled;
      _settings.notificationEnabled = enabled;
      await _saveSettings();

      print('Notifications ${enabled ? 'enabled' : 'disabled'}');
    } catch (e) {
      print('Error changing notification settings: $e');
      Get.snackbar(
        'error'.tr,
        'Failed to change notification settings',
        snackPosition: SnackPosition.bottom,
      );
    }
  }

  /// Change background work settings
  Future<void> changeBackgroundWorkSettings(bool enabled) async {
    try {
      _backgroundWorkEnabled.value = enabled;
      _settings.backgroundWorkEnabled = enabled;
      await _saveSettings();

      print('Background work ${enabled ? 'enabled' : 'disabled'}');
    } catch (e) {
      print('Error changing background work settings: $e');
      Get.snackbar(
        'error'.tr,
        'Failed to change background work settings',
        snackPosition: SnackPosition.bottom,
      );
    }
  }

  /// Save settings to ObjectBox
  Future<void> _saveSettings() async {
    try {
      objectboxService.objectbox.appBox.put(_settings);
      print('Settings saved successfully');
    } catch (e) {
      print('Error saving settings: $e');
      throw e;
    }
  }

  /// Reset all settings to default
  Future<void> resetSettings() async {
    try {
      _settings = Sittings(id: 1);
      _isDarkMode.value = false;
      _notificationsEnabled.value = true;
      _backgroundWorkEnabled.value = true;

      await _saveSettings();
      await localizationService.changeLocale(localizationService.defaultLocale);
      Get.changeThemeMode(ThemeMode.light);

      Get.snackbar(
        'success'.tr,
        'Settings reset successfully',
        snackPosition: SnackPosition.bottom,
      );

      print('Settings reset to default');
    } catch (e) {
      print('Error resetting settings: $e');
      Get.snackbar(
        'error'.tr,
        'Failed to reset settings',
        snackPosition: SnackPosition.bottom,
      );
    }
  }

  /// Get available languages for UI
  List<Locale> get availableLanguages => localizationService.availableLocales;

  /// Get language display name
  String getLanguageDisplayName(Locale locale) {
    return localizationService.getLanguageName(locale);
  }

  /// Legacy method for backward compatibility
  @Deprecated('Use changeLanguage(Locale) instead')
  void changeLang(String lang) {
    Locale locale;
    switch (lang) {
      case 'ar':
        locale = const Locale('ar', 'SA');
        break;
      case 'en':
        locale = const Locale('en', 'US');
        break;
      default:
        locale = localizationService.defaultLocale;
    }
    changeLanguage(locale);
  }

  /// Get current language property for backward compatibility
  Locale? get language => currentLocale;
}
